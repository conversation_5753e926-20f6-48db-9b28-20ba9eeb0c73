import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/entity/diagnostic.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/data/remote/models/diagnostic_model.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';
import 'package:s3g/src/sync/sync_service.dart';
import 'package:uuid/uuid.dart';

@Entity()
class DiagnosticLocalModel {
  @Id()
  int oid = 0;

  @Index()
  @Unique()
  String? id; // Server ID, null for offline-created diagnostics

  @Index()
  @Unique()
  String localId; // Local UUID for identification

  @Index()
  String instanceLocalId; // Reference to instance local ID

  String questionnaireId; // Reference to questionnaire ID
  String response; // Response content (text or file path for attachments)

  String syncStatus; // SyncStatus enum as string
  DateTime? lastSyncAttempt;
  String? syncError;
  bool isDeleted;

  @Property(type: PropertyType.date)
  DateTime createdAt;

  @Property(type: PropertyType.date)
  DateTime updatedAt;

  DiagnosticLocalModel({
    this.id,
    required this.localId,
    required this.instanceLocalId,
    required this.questionnaireId,
    required this.response,
    required this.syncStatus,
    this.lastSyncAttempt,
    this.syncError,
    this.isDeleted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  // REQUIRED: Helper getter for sync check
  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;

  // REQUIRED: Helper getter for offline created check
  bool get isOfflineCreated => id == null;

  // REQUIRED: Helper getter for display ID
  String get displayId => id ?? localId;

  // Factory method to create from domain entity
  factory DiagnosticLocalModel.fromEntity(
    Diagnostic diagnostic, {
    required String instanceLocalId,
  }) {
    return DiagnosticLocalModel(
      id: diagnostic.id,
      localId: const Uuid().v4(),
      instanceLocalId: instanceLocalId,
      questionnaireId: diagnostic.questionnaire.id,
      response: diagnostic.response,
      syncStatus: SyncStatus.synced.value,
      createdAt: diagnostic.createdAt,
      updatedAt: diagnostic.updatedAt,
    );
  }

  // Factory method to create offline diagnostic
  factory DiagnosticLocalModel.createOffline({
    required String instanceLocalId,
    required String questionnaireId,
    required String response,
  }) {
    final now = DateTime.now();

    return DiagnosticLocalModel(
      localId: const Uuid().v4(),
      instanceLocalId: instanceLocalId,
      questionnaireId: questionnaireId,
      response: response,
      syncStatus: SyncStatus.pending.value,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Convert to domain entity (requires questionnaire to be provided)
  Diagnostic toEntity(Questionnaire questionnaire) {
    return Diagnostic(
      id: displayId,
      questionnaire: questionnaire,
      instanceId: instanceLocalId,
      response: response,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Update sync status
  void updateSyncStatus(SyncStatus status, {String? error}) {
    syncStatus = status.value;
    lastSyncAttempt = DateTime.now();
    syncError = error;
  }

  // Mark as synced with server ID
  void markAsSynced(String serverId) {
    id = serverId;
    syncStatus = SyncStatus.synced.value;
    lastSyncAttempt = DateTime.now();
    syncError = null;
  }

  // Update content and mark for sync
  void updateContent({
    String? newQuestionnaireId,
    String? newResponse,
  }) {
    if (newQuestionnaireId != null) questionnaireId = newQuestionnaireId;
    if (newResponse != null) response = newResponse;
    updatedAt = DateTime.now();

    // Mark as needing sync if already synced
    if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
      syncStatus = SyncStatus.pending.value;
    }
  }

  // Mark as deleted
  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();

    if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
      syncStatus = SyncStatus.pending.value;
    }
  }

  // Convert to remote model for API calls
  DiagnosticModel toRemoteModel({
    required String instanceId,
    required Questionnaire questionnaire,
  }) {
    return DiagnosticModel(
      id: id!,
      questionnaire: QuestionnaireModel(
        id: questionnaire.id,
        question: questionnaire.question,
        type: questionnaire.type,
        hint: questionnaire.hint,
        choices: questionnaire.choices?.map((choice) => 
          QuestionnaireChoiceModel(
            id: choice.id,
            questionnaireId: choice.questionnaireId,
            choice: choice.choice,
            createdAt: choice.createdAt,
            updatedAt: choice.updatedAt,
          )
        ).toList(),
        createdAt: questionnaire.createdAt,
        updatedAt: questionnaire.updatedAt,
      ),
      instanceId: instanceId,
      response: response,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
