import 'dart:developer';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/core/utils/attachment_storage_service.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/data/local/diagnostic_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/data/local/models/diagnostic_local_model.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/entity/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/data/local/questionnaire_local_datasource.dart';
import 'package:s3g/src/manager/questionnaire/domain/entity/questionnaire.dart';
import 'package:s3g/src/sync/sync_service.dart';

import '../../domain/repository/diagnostic_repository.dart';
import '../remote/diagnostic_remote_datasource.dart';

@Injectable(as: DiagnosticRepository)
class DiagnosticRepositoryImpl extends DiagnosticRepository {
  final DiagnosticRemoteDataSource _remoteDataSource;
  final DiagnosticLocalDataSource _localDataSource;
  final InstanceLocalDataSource _instanceLocalDataSource;
  final QuestionnaireLocalDataSource _questionnaireLocalDataSource;
  final ConnectionChecker _connectionChecker;
  final SyncService _syncService;
  final AttachmentStorageService _attachmentStorage;

  DiagnosticRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._instanceLocalDataSource,
    this._questionnaireLocalDataSource,
    this._connectionChecker,
    this._syncService,
    this._attachmentStorage,
  );

  @override
  RepositoryResponse<Diagnostic> createDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String response,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    // Get instance local ID for storage
    final instanceModel =
        await _instanceLocalDataSource.getInstanceById(instanceId);

    if (isOnline) {
      // Try server creation
      final serverResponse = await requestHelper(
        () => _remoteDataSource.createDiagnostic(
          instanceId: instanceId,
          questionnaire: questionnaire,
          response: response,
        ),
      );

      // Save to local on success
      if (serverResponse.isRight() && instanceModel != null) {
        final diagnostic = serverResponse.getOrElse((l) => throw l);

        final localDiagnostic = DiagnosticLocalModel.fromEntity(
          diagnostic,
          instanceLocalId: instanceModel.localId,
        );

        await _localDataSource.saveDiagnostic(localDiagnostic);
      }

      return serverResponse;
    } else {
      if (instanceModel == null) {
        return Left(CacheFailure('Instance introuvable'));
      }

      // Create offline
      return _createOfflineDiagnostic(
        instanceLocalId: instanceModel.localId,
        questionnaire: questionnaire,
        response: response,
      );
    }
  }

  Future<Either<Failure, Diagnostic>> _createOfflineDiagnostic({
    required String instanceLocalId,
    required Questionnaire questionnaire,
    required String response,
  }) async {
    try {
      // Save attachment to permanent storage if it's an attachment type
      String? savedResponse = response;
      if (questionnaire.type == QuestionnaireResponseType.ATTACHMENT) {
        savedResponse = await _attachmentStorage.saveAttachment(response);
      }

      final localDiagnostic = DiagnosticLocalModel.createOffline(
        instanceLocalId: instanceLocalId,
        questionnaireId: questionnaire.id,
        response: savedResponse ?? response,
      );

      await _localDataSource.saveDiagnostic(localDiagnostic);

      // Get questionnaire from local storage for entity conversion
      final localQuestionnaire = await _questionnaireLocalDataSource
          .getQuestionnaireById(questionnaire.id);

      if (localQuestionnaire == null) {
        return Left(CacheFailure('Questionnaire introuvable'));
      }

      return Right(localDiagnostic.toEntity(localQuestionnaire.toEntity()));
    } catch (e) {
      log('Error creating offline diagnostic: $e');
      return Left(DatabaseFailure('Erreur de base de données'));
    }
  }

  @override
  RepositoryResponse<MessageResponse> deleteDiagnostic({
    required String instanceId,
    required String diagnosticId,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      // Try server deletion
      final serverResponse = await requestHelper(
        () => _remoteDataSource.deleteDiagnostic(
          instanceId: instanceId,
          diagnosticId: diagnosticId,
        ),
      );

      // Delete from local on success
      if (serverResponse.isRight()) {
        await _localDataSource.deleteDiagnostic(diagnosticId);
      }

      return serverResponse;
    } else {
      // Mark as deleted offline
      try {
        await _localDataSource.markDiagnosticAsDeleted(diagnosticId);
        return Right(
            MessageResponse(message: 'Diagnostic supprimé hors ligne'));
      } catch (e) {
        log('Error deleting diagnostic offline: $e');
        return Left(DatabaseFailure('Erreur de base de données'));
      }
    }
  }

  @override
  RepositoryResponse<Diagnostic> editDiagnostic({
    required String instanceId,
    required Questionnaire questionnaire,
    required String diagnosticId,
    required String response,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      // Try server update
      final serverResponse = await requestHelper(
        () => _remoteDataSource.editDiagnostic(
          instanceId: instanceId,
          questionnaire: questionnaire,
          diagnosticId: diagnosticId,
          response: response,
        ),
      );

      // Update local on success
      if (serverResponse.isRight()) {
        final diagnostic = serverResponse.getOrElse((l) => throw l);
        final instanceModel =
            await _instanceLocalDataSource.getInstanceById(instanceId);

        if (instanceModel != null) {
          final localDiagnostic = DiagnosticLocalModel.fromEntity(
            diagnostic,
            instanceLocalId: instanceModel.localId,
          );
          await _localDataSource.saveDiagnostic(localDiagnostic);
        }
      }

      return serverResponse;
    } else {
      // Update offline
      return _updateOfflineDiagnostic(
        diagnosticId: diagnosticId,
        questionnaire: questionnaire,
        response: response,
      );
    }
  }

  Future<Either<Failure, Diagnostic>> _updateOfflineDiagnostic({
    required String diagnosticId,
    required Questionnaire questionnaire,
    required String response,
  }) async {
    try {
      // Save attachment to permanent storage if it's an attachment type
      String? savedResponse = response;
      if (questionnaire.type == QuestionnaireResponseType.ATTACHMENT) {
        savedResponse = await _attachmentStorage.saveAttachment(response);
      }

      await _localDataSource.updateDiagnosticContent(
        diagnosticId,
        questionnaireId: questionnaire.id,
        response: savedResponse ?? response,
      );

      final updatedDiagnostic =
          await _localDataSource.getDiagnosticById(diagnosticId);
      if (updatedDiagnostic == null) {
        return Left(CacheFailure('Diagnostic introuvable'));
      }

      // Get questionnaire from local storage for entity conversion
      final localQuestionnaire = await _questionnaireLocalDataSource
          .getQuestionnaireById(questionnaire.id);

      if (localQuestionnaire == null) {
        return Left(CacheFailure('Questionnaire introuvable'));
      }

      return Right(updatedDiagnostic.toEntity(localQuestionnaire.toEntity()));
    } catch (e) {
      log('Error updating offline diagnostic: $e');
      return Left(DatabaseFailure('Erreur de base de données'));
    }
  }

  @override
  RepositoryResponse<List<Diagnostic>> getDiagnostics({
    required String instanceId,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    // Get instance local ID for local queries
    final instanceModel =
        await _instanceLocalDataSource.getInstanceById(instanceId);

    if (isOnline) {
      // Try server fetch
      final serverResponse = await requestHelper(
        () => _remoteDataSource.getDiagnostics(instanceId: instanceId),
      );

      // Save to local on success
      if (serverResponse.isRight() && instanceModel != null) {
        final diagnostics = serverResponse.getOrElse((l) => throw l);

        final localDiagnostics = diagnostics
            .map((diagnostic) => DiagnosticLocalModel.fromEntity(
                  diagnostic,
                  instanceLocalId: instanceModel.localId,
                ))
            .toList();

        await _localDataSource.saveDiagnostics(localDiagnostics);
      }

      return serverResponse;
    } else {
      // Return local data
      if (instanceModel == null) {
        return Left(CacheFailure('Instance introuvable'));
      }

      try {
        final localDiagnostics = await _localDataSource
            .getDiagnosticsByInstanceLocalId(instanceModel.localId);

        final diagnostics = <Diagnostic>[];
        for (final localDiagnostic in localDiagnostics) {
          final questionnaire = await _questionnaireLocalDataSource
              .getQuestionnaireById(localDiagnostic.questionnaireId);

          if (questionnaire != null) {
            diagnostics.add(localDiagnostic.toEntity(questionnaire.toEntity()));
          }
        }

        return Right(diagnostics);
      } catch (e) {
        log('Error getting offline diagnostics: $e');
        return Left(DatabaseFailure('Erreur de base de données'));
      }
    }
  }
}
