import 'dart:developer';

import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.g.dart';

// Import all local models for ObjectBox cleanup
import 'package:s3g/src/manager/instance/data/local/models/instance_local_model.dart';
import 'package:s3g/src/companion/followup/data/local/models/followup_local_model.dart';
import 'package:s3g/src/companion/relapse/data/local/models/relapse_local_model.dart';
import 'package:s3g/src/manager/instance_features/relapse/data/local/models/relapse_local_model.dart';
import 'package:s3g/src/manager/instance_features/companion/data/local/models/instance_companion_local_model.dart';
import 'package:s3g/src/companion/companion/data/local/models/companion_local_model.dart';
import 'package:s3g/src/manager/health_center/data/local/models/health_center_local_model.dart';
import 'package:s3g/src/manager/instance_features/treatment/data/local/models/treatment_local_model.dart';
import 'package:s3g/src/manager/member/data/local/models/member_local_model.dart';
import 'package:s3g/src/manager/questionnaire/data/local/models/questionnaire_local_model.dart';
import 'package:s3g/src/authentication/data/local/models/user_local_model.dart';

export 'objectbox.g.dart';

class ObjectBox {
  late final Store store;

  ObjectBox._create(this.store) {
    // Add any additional setup code, e.g. build queries.
  }

  static Future<ObjectBox> create() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final store = await openStore(
      directory: p.join(docsDir.path, "obx-db-s3g"),
    );
    return ObjectBox._create(store);
  }

  /// Clears all data from ObjectBox database
  /// This should be called on logout to ensure user privacy
  static Future<void> clearAllData() async {
    try {
      final objectBox = getIt<ObjectBox>();
      final store = objectBox.store;

      // Get all entity boxes and clear them
      final boxes = [
        store.box<InstanceLocalModel>(),
        store.box<FollowupLocalModel>(),
        store.box<TreatmentLocalModel>(),
        store.box<CompanionRelapseLocalModel>(),
        store.box<InstanceRelapseLocalModel>(),
        store.box<InstanceCompanionLocalModel>(),
        store.box<CompanionLocalModel>(),
        store.box<HealthCenterLocalModel>(),
        store.box<MemberLocalModel>(),
        store.box<QuestionnaireLocalModel>(),
        store.box<UserLocalModel>(),
      ];

      // Clear all boxes
      await Future.wait(
        boxes.map((box) async {
          final entities = await box.getAllAsync();
          for (final entity in entities) {
            bool skipDeletion = false;

            try {
              if ((entity as dynamic).needsSync) {
                skipDeletion = true;
              }
            } catch (e) {
              // Skip deletion if any error occurs
            }

            try {
              if (!skipDeletion) {
                await box.removeAsync((entity as dynamic).oid);
                log('ObjectBox: Deleted entity: ${entity.runtimeType}');
              }
            } catch (e) {
              log('ObjectBox: Error deleting entity: $e');
            }
          }
        }),
      );

      log('ObjectBox: All local data cleared successfully');
    } catch (e) {
      log('ObjectBox: Error clearing data: $e');
      rethrow;
    }
  }

  @Deprecated('Use clearAllData() instead')
  static deleteAllFiles() async {
    getIt<ObjectBox>().store.box().removeAllAsync();
  }
}
